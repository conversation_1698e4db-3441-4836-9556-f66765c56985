# FLOP Counting in vLLM

This document describes how to use the FLOP (Floating Point Operations) counting feature in vLLM for performance analysis and GPU utilization debugging.

## Overview

The FLOP counting feature helps you:
- **Monitor computational load** during model serving
- **Debug GPU utilization** issues and bottlenecks
- **Analyze performance characteristics** of different models
- **Track FLOPs per second** (computational throughput)
- **Optimize serving configuration** for better performance

## Quick Start

### Enable FLOP Counting

```bash
# Method 1: Command line flag
python -m vllm.entrypoints.openai.api_server \
    --model facebook/opt-125m \
    --collect-flop-stats

# Method 2: Environment variable
export VLLM_COLLECT_FLOP_STATS=1
python -m vllm.entrypoints.openai.api_server --model facebook/opt-125m
```

### Expected Output

```
INFO: FLOPCounter initialized for model with 12 layers, 768 hidden size, 12 attention heads
INFO: Engine 000: Avg prompt throughput: 150.2 tokens/s, Avg generation throughput: 45.8 tokens/s, Running: 2 reqs, Waiting: 0 reqs, GPU KV cache usage: 12.5%, Prefix cache hit rate: 0.0%, FLOPs/s: 2.34e+12
```

## Detailed Usage

### OpenAI API Server

```bash
python -m vllm.entrypoints.openai.api_server \
    --model facebook/opt-125m \
    --collect-flop-stats \
    --port 8000

# Test with curl
curl http://localhost:8000/v1/completions \
    -H "Content-Type: application/json" \
    -d '{
        "model": "facebook/opt-125m",
        "prompt": "Hello, how are you?",
        "max_tokens": 50
    }'
```

### Python API

```python
from vllm import LLM, SamplingParams

# FLOP counting is enabled via command line or environment variable
llm = LLM(model="facebook/opt-125m")

prompts = ["Hello, how are you?", "What is machine learning?"]
sampling_params = SamplingParams(temperature=0.8, max_tokens=50)

outputs = llm.generate(prompts, sampling_params)
# Check logs for FLOP information
```

## Metrics and Monitoring

### Prometheus Metrics

When FLOP counting is enabled, these metrics are available at `/metrics`:

```
# Total FLOPs processed (counter)
vllm:total_flops{model_name="facebook/opt-125m",engine="0"} 1.234567e+15

# FLOPs per second (gauge)
vllm:flops_per_second{model_name="facebook/opt-125m",engine="0"} 2.34e+12
```

### Console Logs

FLOP information appears in the regular serving logs:

```
Engine 000: Avg prompt throughput: 150.2 tokens/s, Avg generation throughput: 45.8 tokens/s, Running: 2 reqs, Waiting: 0 reqs, GPU KV cache usage: 12.5%, Prefix cache hit rate: 0.0%, FLOPs/s: 2.34e+12
```

## Performance Analysis

### Understanding FLOP/s Values

| FLOPs/s Range | Interpretation | Action |
|---------------|----------------|---------|
| > 1e12 | Good GPU utilization | Monitor and maintain |
| 1e11 - 1e12 | Moderate utilization | Investigate optimization |
| < 1e11 | Poor utilization | Check for bottlenecks |

### Common Bottlenecks

1. **Memory Bound**: High GPU memory usage, low FLOPs/s
   - Solution: Reduce batch size, use quantization
   
2. **Compute Bound**: Low GPU memory usage, low FLOPs/s
   - Solution: Increase batch size, optimize model

3. **Scheduling Issues**: Irregular FLOPs/s patterns
   - Solution: Tune scheduler parameters

### Optimization Workflow

1. **Baseline Measurement**
   ```bash
   # Measure current performance
   python -m vllm.entrypoints.openai.api_server \
       --model your-model \
       --collect-flop-stats
   ```

2. **Batch Size Tuning**
   ```bash
   # Test different batch sizes
   for batch_size in 1 4 8 16 32; do
       python -m vllm.entrypoints.openai.api_server \
           --model your-model \
           --max-num-seqs $batch_size \
           --collect-flop-stats
   done
   ```

3. **Monitor and Compare**
   - Record FLOPs/s for each configuration
   - Choose optimal batch size for your workload

## Implementation Details

### FLOP Calculation

The FLOP counter estimates theoretical FLOPs using:

```python
# Simplified formula for transformer models
attention_flops = batch_size * seq_len^2 * hidden_size * num_layers
mlp_flops = batch_size * seq_len * hidden_size * intermediate_size * 2 * num_layers
total_flops = attention_flops + mlp_flops
```

### Performance Impact

- **Minimal overhead**: Only theoretical calculations
- **No model execution impact**: Computed once per forward pass
- **Efficient implementation**: Simple arithmetic operations

## Troubleshooting

### FLOP Counting Not Working

1. **Check flag is enabled**:
   ```bash
   # Ensure --collect-flop-stats is used
   ps aux | grep vllm | grep collect-flop-stats
   ```

2. **Verify logs**:
   ```bash
   # Look for initialization message
   grep "FLOPCounter initialized" /path/to/logs
   ```

3. **Check environment**:
   ```bash
   echo $VLLM_COLLECT_FLOP_STATS
   ```

### Missing Metrics

1. **Prometheus not configured**: Check `/metrics` endpoint
2. **Stats logging disabled**: Ensure logging is enabled
3. **No requests processed**: Send test requests

### Performance Issues

1. **FLOP counting overhead**: Should be negligible
2. **Other bottlenecks**: Check GPU memory, CPU usage
3. **Model-specific issues**: Some models may not support FLOP counting

## Examples

### Basic Performance Test

```python
# examples/flop_counting_example.py
import time
from vllm import LLM, SamplingParams

llm = LLM(model="facebook/opt-125m")
prompts = ["Hello"] * 10
sampling_params = SamplingParams(max_tokens=50)

start_time = time.time()
outputs = llm.generate(prompts, sampling_params)
end_time = time.time()

print(f"Generated {len(outputs)} responses in {end_time - start_time:.2f}s")
# Check logs for FLOPs/s information
```

### Batch Size Optimization

```bash
#!/bin/bash
# Test different batch sizes and record FLOPs/s

for batch_size in 1 2 4 8 16; do
    echo "Testing batch size: $batch_size"
    python -m vllm.entrypoints.openai.api_server \
        --model facebook/opt-125m \
        --max-num-seqs $batch_size \
        --collect-flop-stats \
        --port 8000 &
    
    sleep 10  # Wait for server to start
    
    # Send test requests and record FLOPs/s
    curl -s http://localhost:8000/v1/completions \
        -H "Content-Type: application/json" \
        -d '{"model": "facebook/opt-125m", "prompt": "Test", "max_tokens": 10}'
    
    pkill -f vllm.entrypoints.openai.api_server
    sleep 5
done
```

## Best Practices

1. **Enable for debugging**: Use FLOP counting during development and optimization
2. **Monitor trends**: Track FLOPs/s over time to detect performance regressions
3. **Compare configurations**: Use FLOP metrics to choose optimal settings
4. **Disable in production**: Consider disabling if not needed for monitoring
5. **Combine with other metrics**: Use alongside GPU utilization, memory usage, etc.

## Contributing

To extend FLOP counting functionality:

1. **Add new architectures**: Modify `vllm/metrics/flop_counter.py`
2. **Enhance metrics**: Update `vllm/v1/metrics/loggers.py`
3. **Add tests**: Create tests for new functionality
4. **Update documentation**: Keep this guide current

For questions or issues, please refer to the vLLM GitHub repository.
