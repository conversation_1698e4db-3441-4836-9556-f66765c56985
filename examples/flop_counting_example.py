#!/usr/bin/env python3
"""
Example script demonstrating FLOP counting in vLLM for performance analysis.

This script shows how to:
1. Enable FLOP counting
2. Monitor serving FLOPs
3. Use FLOP metrics for performance debugging

Usage:
    python examples/flop_counting_example.py --model facebook/opt-125m
"""

import argparse
import time
from vllm import LLM, SamplingParams


def main():
    parser = argparse.ArgumentParser(description="FLOP counting example for vLLM")
    parser.add_argument("--model", type=str, default="facebook/opt-125m",
                       help="Model to use for testing")
    parser.add_argument("--max-tokens", type=int, default=50,
                       help="Maximum tokens to generate")
    parser.add_argument("--num-prompts", type=int, default=5,
                       help="Number of prompts to test")
    args = parser.parse_args()

    print("=" * 60)
    print("vLLM FLOP Counting Example")
    print("=" * 60)
    print(f"Model: {args.model}")
    print(f"Max tokens: {args.max_tokens}")
    print(f"Number of prompts: {args.num_prompts}")
    print()

    # Test prompts of varying lengths
    prompts = [
        "Hello",
        "Hello, how are you?",
        "Hello, how are you today? I hope you're doing well.",
        "Write a short story about a robot learning to paint.",
        "Explain the concept of machine learning in simple terms that a child could understand.",
    ][:args.num_prompts]

    # Sampling parameters
    sampling_params = SamplingParams(
        temperature=0.8,
        top_p=0.95,
        max_tokens=args.max_tokens,
    )

    print("Creating LLM with FLOP counting enabled...")
    print("Note: FLOP counting is enabled via --collect-flop-stats flag or environment variable")
    print()

    # Create LLM instance
    # Note: In practice, you would enable FLOP counting via:
    # 1. Command line: --collect-flop-stats
    # 2. Environment variable: VLLM_COLLECT_FLOP_STATS=1
    # 3. Or programmatically via ObservabilityConfig

    try:
        llm = LLM(model=args.model)

        print("Generating responses...")
        print("Watch the logs for FLOP information:")
        print("- FLOPCounter initialization")
        print("- FLOPs/s in serving logs")
        print()

        start_time = time.time()

        # Generate responses
        outputs = llm.generate(prompts, sampling_params)

        end_time = time.time()
        total_time = end_time - start_time

        print("Results:")
        print("-" * 40)

        total_input_tokens = 0
        total_output_tokens = 0

        for i, output in enumerate(outputs):
            prompt = output.prompt
            generated_text = output.outputs[0].text

            # Estimate token counts (rough approximation)
            input_tokens = len(prompt.split()) * 1.3  # Rough estimate
            output_tokens = len(generated_text.split()) * 1.3

            total_input_tokens += input_tokens
            total_output_tokens += output_tokens

            print(f"Prompt {i+1}:")
            print(f"  Input: {prompt[:50]}{'...' if len(prompt) > 50 else ''}")
            print(f"  Output: {generated_text[:50]}{'...' if len(generated_text) > 50 else ''}")
            print(f"  Est. tokens: {input_tokens:.0f} input, {output_tokens:.0f} output")
            print()

        # Performance summary
        print("Performance Summary:")
        print("-" * 40)
        print(f"Total time: {total_time:.2f} seconds")
        print(f"Total input tokens: {total_input_tokens:.0f}")
        print(f"Total output tokens: {total_output_tokens:.0f}")
        print(f"Input throughput: {total_input_tokens/total_time:.1f} tokens/s")
        print(f"Output throughput: {total_output_tokens/total_time:.1f} tokens/s")
        print()

        print("FLOP Analysis:")
        print("-" * 40)
        print("Look for these in the logs:")
        print("1. 'FLOPCounter initialized' - confirms FLOP counting is enabled")
        print("2. 'FLOPs/s: X.XXe+XX' - computational throughput")
        print("3. Prometheus metrics at /metrics endpoint")
        print()

        print("Performance Tips:")
        print("-" * 40)
        print("• High FLOPs/s (>1e12) indicates good GPU utilization")
        print("• Low FLOPs/s may indicate memory or scheduling bottlenecks")
        print("• Compare FLOPs/s across different batch sizes and models")
        print("• Use FLOP metrics to optimize serving configuration")

    except Exception as e:
        print(f"Error: {e}")
        print()
        print("To enable FLOP counting, use one of these methods:")
        print("1. Command line flag: --collect-flop-stats")
        print("2. Environment variable: export VLLM_COLLECT_FLOP_STATS=1")
        print("3. API server: python -m vllm.entrypoints.openai.api_server --collect-flop-stats")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
