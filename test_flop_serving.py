#!/usr/bin/env python3
"""
Test script to verify FLOP counting functionality in vLLM.
This script demonstrates how to enable FLOP counting and shows the serving FLOPs output.
"""

import argparse
import logging

# Set up logging to see FLOP output
logging.basicConfig(level=logging.INFO)

def test_with_openai_server():
    """Test FLOP counting with OpenAI API server."""
    print("=" * 60)
    print("Testing FLOP Counting with OpenAI API Server")
    print("=" * 60)

    print("\n1. Start vLLM server with FLOP counting enabled:")
    print("   python -m vllm.entrypoints.openai.api_server \\")
    print("       --model facebook/opt-125m \\")
    print("       --collect-flop-stats \\")
    print("       --port 8000")

    print("\n2. Send requests to see FLOP output in server logs:")
    print("   curl http://localhost:8000/v1/completions \\")
    print("       -H 'Content-Type: application/json' \\")
    print("       -d '{")
    print("         \"model\": \"facebook/opt-125m\",")
    print("         \"prompt\": \"Hello, how are you?\",")
    print("         \"max_tokens\": 50")
    print("       }'")

    print("\n3. Expected output in server logs:")
    print("   INFO: FLOPCounter initialized for model with X layers, Y hidden size, Z attention heads")
    print("   INFO: Engine 000: Avg prompt throughput: X.X tokens/s, ... FLOPs/s: X.XXe+XX")

    print("\n4. Prometheus metrics available at http://localhost:8000/metrics:")
    print("   - vllm:total_flops")
    print("   - vllm:flops_per_second")


def test_with_simple_script():
    """Show a simple script example."""
    print("\n" + "=" * 60)
    print("Simple Python Script Example")
    print("=" * 60)

    script_content = '''
from vllm import LLM, SamplingParams

# Create LLM with FLOP counting enabled
llm = LLM(
    model="facebook/opt-125m",
    # Enable FLOP counting via environment variable or config
)

# Generate text
prompts = ["Hello, how are you?", "What is the weather like?"]
sampling_params = SamplingParams(temperature=0.8, top_p=0.95, max_tokens=50)

outputs = llm.generate(prompts, sampling_params)

for output in outputs:
    prompt = output.prompt
    generated_text = output.outputs[0].text
    print(f"Prompt: {prompt!r}, Generated text: {generated_text!r}")
'''

    print("Example script:")
    print(script_content)

    print("To enable FLOP counting, set environment variable:")
    print("export VLLM_COLLECT_FLOP_STATS=1")
    print("or use the --collect-flop-stats flag")


def show_expected_output():
    """Show what the FLOP output should look like."""
    print("\n" + "=" * 60)
    print("Expected FLOP Output Examples")
    print("=" * 60)

    print("\n1. Initialization Log:")
    print("INFO 12-20 10:30:15 flop_counter.py:41] FLOPCounter initialized for model with 12 layers, 768 hidden size, 12 attention heads")

    print("\n2. Serving Logs with FLOP/s:")
    print("INFO 12-20 10:30:20 loggers.py:150] Engine 000: Avg prompt throughput: 150.2 tokens/s, Avg generation throughput: 45.8 tokens/s, Running: 2 reqs, Waiting: 0 reqs, GPU KV cache usage: 12.5%, Prefix cache hit rate: 0.0%, FLOPs/s: 2.34e+12")

    print("\n3. Prometheus Metrics:")
    print("# HELP vllm:total_flops Total FLOPs (Floating Point Operations) processed.")
    print("# TYPE vllm:total_flops counter")
    print('vllm:total_flops{model_name="facebook/opt-125m",engine="0"} 1.234567e+15')
    print("")
    print("# HELP vllm:flops_per_second FLOPs per second (computational throughput).")
    print("# TYPE vllm:flops_per_second gauge")
    print('vllm:flops_per_second{model_name="facebook/opt-125m",engine="0"} 2.34e+12')

    print("\n4. Performance Analysis:")
    print("- High FLOPs/s (>1e12): Good GPU utilization")
    print("- Low FLOPs/s (<1e11): Potential bottleneck (memory, scheduling, etc.)")
    print("- Compare FLOPs/s across different models and configurations")


def show_debugging_tips():
    """Show how to use FLOP counting for debugging."""
    print("\n" + "=" * 60)
    print("Using FLOP Counting for Performance Debugging")
    print("=" * 60)

    print("\n1. GPU Utilization Analysis:")
    print("   - Monitor FLOPs/s to identify underutilized GPU")
    print("   - Compare theoretical peak FLOPs with measured FLOPs/s")
    print("   - Look for patterns in FLOP/s drops")

    print("\n2. Batch Size Optimization:")
    print("   - Increase batch size and monitor FLOPs/s")
    print("   - Find optimal batch size for maximum FLOPs/s")
    print("   - Balance FLOPs/s with latency requirements")

    print("\n3. Model Comparison:")
    print("   - Compare FLOPs/s between different model sizes")
    print("   - Analyze computational efficiency per parameter")
    print("   - Identify most efficient model for your workload")

    print("\n4. Bottleneck Identification:")
    print("   - Low FLOPs/s + High GPU memory usage = Memory bound")
    print("   - Low FLOPs/s + Low GPU memory usage = Compute bound")
    print("   - Irregular FLOPs/s patterns = Scheduling issues")


def main():
    parser = argparse.ArgumentParser(description="Test and demonstrate FLOP counting in vLLM")
    parser.add_argument("--mode", choices=["server", "script", "output", "debug", "all"],
                       default="all", help="What to demonstrate")
    args = parser.parse_args()

    print("vLLM FLOP Counting Test and Demo")
    print("This script shows how to enable and use FLOP counting for performance debugging")

    if args.mode in ["server", "all"]:
        test_with_openai_server()

    if args.mode in ["script", "all"]:
        test_with_simple_script()

    if args.mode in ["output", "all"]:
        show_expected_output()

    if args.mode in ["debug", "all"]:
        show_debugging_tips()

    print("\n" + "=" * 60)
    print("Summary")
    print("=" * 60)
    print("FLOP counting in vLLM helps with:")
    print("✓ Performance debugging and optimization")
    print("✓ GPU utilization analysis")
    print("✓ Model efficiency comparison")
    print("✓ Bottleneck identification")
    print("\nTo enable: Use --collect-flop-stats flag or set VLLM_COLLECT_FLOP_STATS=1")
    print("Monitor: FLOPs/s in logs and Prometheus metrics")


if __name__ == "__main__":
    main()
