# SPDX-License-Identifier: Apache-2.0

"""
FLOP (Floating Point Operations) counter for vLLM.

This module provides utilities to calculate theoretical FLOPs for transformer models
during inference, helping with performance analysis and GPU utilization debugging.
"""

import math
from typing import Optional, Tuple

from vllm.config import ModelConfig
from vllm.logger import init_logger

logger = init_logger(__name__)


class FLOPCounter:
    """
    Calculates theoretical FLOPs for transformer model inference.
    
    This counter estimates FLOPs based on model architecture and input dimensions,
    providing insights into computational load during serving.
    """
    
    def __init__(self, model_config: ModelConfig):
        self.model_config = model_config
        self.hidden_size = getattr(model_config.hf_config, 'hidden_size', 0)
        self.num_layers = getattr(model_config.hf_config, 'num_hidden_layers', 0)
        self.num_attention_heads = getattr(model_config.hf_config, 'num_attention_heads', 0)
        self.intermediate_size = getattr(model_config.hf_config, 'intermediate_size', 0)
        self.vocab_size = getattr(model_config.hf_config, 'vocab_size', 0)
        
        # Calculate head dimension
        self.head_dim = self.hidden_size // self.num_attention_heads if self.num_attention_heads > 0 else 0
        
        # Get number of key-value heads for GQA/MQA
        self.num_kv_heads = getattr(model_config.hf_config, 'num_key_value_heads', self.num_attention_heads)
        
        logger.info(f"FLOPCounter initialized for model with {self.num_layers} layers, "
                   f"{self.hidden_size} hidden size, {self.num_attention_heads} attention heads")
    
    def count_attention_flops(self, batch_size: int, seq_len: int, 
                            kv_cache_len: int = 0, is_prefill: bool = True) -> int:
        """
        Calculate FLOPs for attention computation.
        
        Args:
            batch_size: Number of sequences in the batch
            seq_len: Length of input sequences (for prefill) or 1 (for decode)
            kv_cache_len: Length of KV cache (relevant for decode phase)
            is_prefill: Whether this is prefill (True) or decode (False) phase
            
        Returns:
            Number of FLOPs for attention computation
        """
        if self.num_attention_heads == 0 or self.head_dim == 0:
            return 0
            
        flops = 0
        
        # QKV projection FLOPs: batch_size * seq_len * hidden_size * (hidden_size + 2 * num_kv_heads * head_dim)
        qkv_proj_flops = batch_size * seq_len * self.hidden_size * (
            self.hidden_size + 2 * self.num_kv_heads * self.head_dim
        )
        flops += qkv_proj_flops
        
        if is_prefill:
            # Prefill: attention over full sequence
            # QK^T: batch_size * num_heads * seq_len * seq_len * head_dim
            qk_flops = batch_size * self.num_attention_heads * seq_len * seq_len * self.head_dim
            
            # Attention weights * V: batch_size * num_heads * seq_len * seq_len * head_dim
            av_flops = batch_size * self.num_attention_heads * seq_len * seq_len * self.head_dim
            
            flops += qk_flops + av_flops
        else:
            # Decode: attention over KV cache + new token
            total_seq_len = kv_cache_len + 1
            
            # QK^T: batch_size * num_heads * 1 * total_seq_len * head_dim
            qk_flops = batch_size * self.num_attention_heads * 1 * total_seq_len * self.head_dim
            
            # Attention weights * V: batch_size * num_heads * 1 * total_seq_len * head_dim
            av_flops = batch_size * self.num_attention_heads * 1 * total_seq_len * self.head_dim
            
            flops += qk_flops + av_flops
        
        # Output projection: batch_size * seq_len * hidden_size * hidden_size
        out_proj_flops = batch_size * seq_len * self.hidden_size * self.hidden_size
        flops += out_proj_flops
        
        return flops
    
    def count_mlp_flops(self, batch_size: int, seq_len: int) -> int:
        """
        Calculate FLOPs for MLP (feed-forward) computation.
        
        Args:
            batch_size: Number of sequences in the batch
            seq_len: Length of sequences
            
        Returns:
            Number of FLOPs for MLP computation
        """
        if self.intermediate_size == 0:
            return 0
            
        # Up projection: batch_size * seq_len * hidden_size * intermediate_size
        up_proj_flops = batch_size * seq_len * self.hidden_size * self.intermediate_size
        
        # Down projection: batch_size * seq_len * intermediate_size * hidden_size
        down_proj_flops = batch_size * seq_len * self.intermediate_size * self.hidden_size
        
        # Some models have gated MLPs (e.g., LLaMA) which double the up projection
        # For simplicity, we assume standard MLP here, but this could be extended
        
        return up_proj_flops + down_proj_flops
    
    def count_embedding_flops(self, batch_size: int, seq_len: int) -> int:
        """
        Calculate FLOPs for embedding lookup.
        
        Note: Embedding lookup is typically not counted as FLOPs since it's memory access,
        but we include it for completeness.
        
        Args:
            batch_size: Number of sequences in the batch
            seq_len: Length of sequences
            
        Returns:
            Number of FLOPs for embedding (typically 0)
        """
        # Embedding lookup is memory access, not computation
        # But if we want to count it: batch_size * seq_len * hidden_size
        return 0
    
    def count_layernorm_flops(self, batch_size: int, seq_len: int) -> int:
        """
        Calculate FLOPs for layer normalization.
        
        Args:
            batch_size: Number of sequences in the batch
            seq_len: Length of sequences
            
        Returns:
            Number of FLOPs for layer normalization
        """
        # LayerNorm: mean, variance, normalize, scale, shift
        # Approximately 5 operations per element
        return 5 * batch_size * seq_len * self.hidden_size
    
    def count_total_flops(self, batch_size: int, num_prompt_tokens: int, 
                         num_decode_tokens: int, kv_cache_len: int = 0) -> Tuple[int, int, int, int, int]:
        """
        Calculate total FLOPs for a forward pass.
        
        Args:
            batch_size: Number of sequences in the batch
            num_prompt_tokens: Number of prompt tokens (for prefill)
            num_decode_tokens: Number of decode tokens
            kv_cache_len: Length of KV cache
            
        Returns:
            Tuple of (total_flops, attention_flops, mlp_flops, embedding_flops, layernorm_flops)
        """
        total_attention_flops = 0
        total_mlp_flops = 0
        total_embedding_flops = 0
        total_layernorm_flops = 0
        
        # Prefill phase
        if num_prompt_tokens > 0:
            # Attention FLOPs for prefill
            prefill_attention_flops = self.count_attention_flops(
                batch_size, num_prompt_tokens, kv_cache_len, is_prefill=True
            ) * self.num_layers
            
            # MLP FLOPs for prefill
            prefill_mlp_flops = self.count_mlp_flops(batch_size, num_prompt_tokens) * self.num_layers
            
            # LayerNorm FLOPs for prefill (pre and post attention, pre and post MLP)
            prefill_layernorm_flops = self.count_layernorm_flops(batch_size, num_prompt_tokens) * self.num_layers * 4
            
            # Embedding FLOPs for prefill
            prefill_embedding_flops = self.count_embedding_flops(batch_size, num_prompt_tokens)
            
            total_attention_flops += prefill_attention_flops
            total_mlp_flops += prefill_mlp_flops
            total_layernorm_flops += prefill_layernorm_flops
            total_embedding_flops += prefill_embedding_flops
        
        # Decode phase
        if num_decode_tokens > 0:
            # For decode, we process one token at a time
            decode_seq_len = 1
            current_kv_len = kv_cache_len + num_prompt_tokens
            
            for _ in range(num_decode_tokens):
                # Attention FLOPs for decode
                decode_attention_flops = self.count_attention_flops(
                    batch_size, decode_seq_len, current_kv_len, is_prefill=False
                ) * self.num_layers
                
                # MLP FLOPs for decode
                decode_mlp_flops = self.count_mlp_flops(batch_size, decode_seq_len) * self.num_layers
                
                # LayerNorm FLOPs for decode
                decode_layernorm_flops = self.count_layernorm_flops(batch_size, decode_seq_len) * self.num_layers * 4
                
                # Embedding FLOPs for decode
                decode_embedding_flops = self.count_embedding_flops(batch_size, decode_seq_len)
                
                total_attention_flops += decode_attention_flops
                total_mlp_flops += decode_mlp_flops
                total_layernorm_flops += decode_layernorm_flops
                total_embedding_flops += decode_embedding_flops
                
                current_kv_len += 1
        
        total_flops = total_attention_flops + total_mlp_flops + total_embedding_flops + total_layernorm_flops
        
        return total_flops, total_attention_flops, total_mlp_flops, total_embedding_flops, total_layernorm_flops


def create_flop_counter(model_config: ModelConfig) -> Optional[FLOPCounter]:
    """
    Create a FLOP counter for the given model configuration.
    
    Args:
        model_config: Model configuration
        
    Returns:
        FLOPCounter instance or None if FLOP counting is not supported for this model
    """
    try:
        return FLOPCounter(model_config)
    except Exception as e:
        logger.warning(f"Failed to create FLOP counter: {e}")
        return None
